"""
Module 5: Complexity and Entropy Assessment

This module evaluates signal complexity and entropy measures to assess
neural content richness and signal predictability using multiple entropy metrics.
"""

import numpy as np
from typing import Dict, Any, List
from ..core.base_module import BaseQualityModule
from ..utils.metrics import (
    sample_entropy, approximate_entropy, lempel_ziv_complexity,
    spectral_entropy, hjorth_parameters
)
from ..utils.validation import sanitize_nan


class ComplexityEntropyAssessor(BaseQualityModule):
    """Complexity and Entropy Assessment Module"""
    
    def __init__(self, sampling_rate: float, config: Dict[str, Any] = None):
        super().__init__("Complexity and Entropy", sampling_rate, config)
        
        # Default thresholds and parameters
        self.thresholds = {
            'sample_entropy': {
                'm': self.config.get('sample_entropy', {}).get('m', 2),
                'r_factor': self.config.get('sample_entropy', {}).get('r_factor', 0.2),
                'optimal_range': self.config.get('sample_entropy', {}).get('optimal_range', [0.5, 2.0])
            },
            'approximate_entropy': {
                'm': self.config.get('approximate_entropy', {}).get('m', 2),
                'r_factor': self.config.get('approximate_entropy', {}).get('r_factor', 0.2),
                'optimal_range': self.config.get('approximate_entropy', {}).get('optimal_range', [0.4, 1.5])
            },
            'lempel_ziv': {
                'normalize': self.config.get('lempel_ziv', {}).get('normalize', True),
                'optimal_range': self.config.get('lempel_ziv', {}).get('optimal_range', [0.3, 0.8])
            },
            'spectral_entropy': {
                'optimal_range': self.config.get('spectral_entropy', {}).get('optimal_range', [0.6, 0.9])
            },
            'hjorth_parameters': {
                'complexity_range': self.config.get('hjorth_parameters', {}).get('complexity_range', [1.2, 2.5]),
                'mobility_range': self.config.get('hjorth_parameters', {}).get('mobility_range', [0.5, 2.0])
            }
        }
    
    def analyze(self, signal: np.ndarray, metadata: dict = None) -> dict:
        """
        Perform complexity and entropy analysis
        
        Parameters:
        - signal: EEG signal data (channels x samples or 1D)
        - metadata: Signal metadata
        
        Returns:
        - results: Analysis results dictionary
        """
        # Validate input
        self.validate_signal(signal)
        
        # Handle single channel vs multi-channel
        if signal.ndim == 1:
            signal = signal.reshape(1, -1)
        
        # Initialize results
        self.results = {}
        self.flags = []
        self.recommendations = []
        self.visualizations = []
        
        # Analyze each channel
        channel_results = []
        for ch_idx in range(signal.shape[0]):
            ch_data = signal[ch_idx, :]
            ch_result = self._analyze_channel(ch_data, ch_idx)
            channel_results.append(ch_result)
        
        # Aggregate results across channels
        self._aggregate_channel_results(channel_results)
        
        # Generate visualizations
        self._generate_visualizations(signal, channel_results)
        
        return self.results
    
    def _analyze_channel(self, data: np.ndarray, channel_idx: int) -> Dict[str, Any]:
        """Analyze single channel for complexity and entropy"""
        
        # Clean data
        clean_data = sanitize_nan(data, method="interpolate")
        
        # Sample Entropy
        samp_ent = self._calculate_sample_entropy(clean_data)
        
        # Approximate Entropy
        app_ent = self._calculate_approximate_entropy(clean_data)
        
        # Lempel-Ziv Complexity
        lz_complexity = self._calculate_lz_complexity(clean_data)
        
        # Spectral Entropy
        spec_ent = self._calculate_spectral_entropy(clean_data)
        
        # Hjorth Parameters
        hjorth_mob, hjorth_comp = self._calculate_hjorth_parameters(clean_data)
        
        # Multiscale entropy (simplified version)
        multiscale_ent = self._calculate_multiscale_entropy(clean_data)
        
        # Permutation entropy
        perm_ent = self._calculate_permutation_entropy(clean_data)
        
        # Compile channel results
        channel_result = {
            'channel_idx': channel_idx,
            'sample_entropy': samp_ent['value'],
            'sample_entropy_score': samp_ent['score'],
            'approximate_entropy': app_ent['value'],
            'approximate_entropy_score': app_ent['score'],
            'lempel_ziv_complexity': lz_complexity['value'],
            'lempel_ziv_score': lz_complexity['score'],
            'spectral_entropy': spec_ent['value'],
            'spectral_entropy_score': spec_ent['score'],
            'hjorth_mobility': hjorth_mob,
            'hjorth_complexity': hjorth_comp,
            'hjorth_mobility_score': self._score_hjorth_mobility(hjorth_mob),
            'hjorth_complexity_score': self._score_hjorth_complexity(hjorth_comp),
            'multiscale_entropy': multiscale_ent,
            'permutation_entropy': perm_ent,
            'overall_complexity_score': 0  # Will be calculated
        }
        
        # Calculate overall complexity score for this channel
        channel_result['overall_complexity_score'] = self._calculate_channel_complexity_score(channel_result)
        
        # Generate flags for this channel
        self._generate_channel_flags(channel_result, channel_idx)
        
        return channel_result
    
    def _calculate_sample_entropy(self, data: np.ndarray) -> Dict[str, float]:
        """Calculate Sample Entropy with scoring"""
        
        params = self.thresholds['sample_entropy']
        r = params['r_factor'] * np.std(data)
        
        try:
            samp_ent = sample_entropy(data, m=params['m'], r=r)
        except:
            samp_ent = 0.0
        
        # Score based on optimal range
        optimal_range = params['optimal_range']
        if optimal_range[0] <= samp_ent <= optimal_range[1]:
            score = 1.0
        elif samp_ent < optimal_range[0]:
            score = samp_ent / optimal_range[0] if optimal_range[0] > 0 else 0
        else:
            score = optimal_range[1] / samp_ent if samp_ent > 0 else 0
        
        return {'value': samp_ent, 'score': min(1.0, score)}
    
    def _calculate_approximate_entropy(self, data: np.ndarray) -> Dict[str, float]:
        """Calculate Approximate Entropy with scoring"""
        
        params = self.thresholds['approximate_entropy']
        r = params['r_factor'] * np.std(data)
        
        try:
            app_ent = approximate_entropy(data, m=params['m'], r=r)
        except:
            app_ent = 0.0
        
        # Score based on optimal range
        optimal_range = params['optimal_range']
        if optimal_range[0] <= app_ent <= optimal_range[1]:
            score = 1.0
        elif app_ent < optimal_range[0]:
            score = app_ent / optimal_range[0] if optimal_range[0] > 0 else 0
        else:
            score = optimal_range[1] / app_ent if app_ent > 0 else 0
        
        return {'value': app_ent, 'score': min(1.0, score)}
    
    def _calculate_lz_complexity(self, data: np.ndarray) -> Dict[str, float]:
        """Calculate Lempel-Ziv Complexity with scoring"""
        
        params = self.thresholds['lempel_ziv']
        
        try:
            lz_comp = lempel_ziv_complexity(data, normalize=params['normalize'])
        except:
            lz_comp = 0.0
        
        # Score based on optimal range
        optimal_range = params['optimal_range']
        if optimal_range[0] <= lz_comp <= optimal_range[1]:
            score = 1.0
        elif lz_comp < optimal_range[0]:
            score = lz_comp / optimal_range[0] if optimal_range[0] > 0 else 0
        else:
            score = optimal_range[1] / lz_comp if lz_comp > 0 else 0
        
        return {'value': lz_comp, 'score': min(1.0, score)}
    
    def _calculate_spectral_entropy(self, data: np.ndarray) -> Dict[str, float]:
        """Calculate Spectral Entropy with scoring"""
        
        try:
            spec_ent = spectral_entropy(data, self.sampling_rate)
        except:
            spec_ent = 0.0
        
        # Score based on optimal range
        optimal_range = self.thresholds['spectral_entropy']['optimal_range']
        if optimal_range[0] <= spec_ent <= optimal_range[1]:
            score = 1.0
        elif spec_ent < optimal_range[0]:
            score = spec_ent / optimal_range[0] if optimal_range[0] > 0 else 0
        else:
            score = optimal_range[1] / spec_ent if spec_ent > 0 else 0
        
        return {'value': spec_ent, 'score': min(1.0, score)}
    
    def _calculate_hjorth_parameters(self, data: np.ndarray) -> tuple:
        """Calculate Hjorth Mobility and Complexity"""
        
        try:
            mobility, complexity = hjorth_parameters(data)
        except:
            mobility, complexity = 0.0, 0.0
        
        return mobility, complexity
    
    def _score_hjorth_mobility(self, mobility: float) -> float:
        """Score Hjorth Mobility parameter"""
        
        optimal_range = self.thresholds['hjorth_parameters']['mobility_range']
        if optimal_range[0] <= mobility <= optimal_range[1]:
            return 1.0
        elif mobility < optimal_range[0]:
            return mobility / optimal_range[0] if optimal_range[0] > 0 else 0
        else:
            return optimal_range[1] / mobility if mobility > 0 else 0
    
    def _score_hjorth_complexity(self, complexity: float) -> float:
        """Score Hjorth Complexity parameter"""
        
        optimal_range = self.thresholds['hjorth_parameters']['complexity_range']
        if optimal_range[0] <= complexity <= optimal_range[1]:
            return 1.0
        elif complexity < optimal_range[0]:
            return complexity / optimal_range[0] if optimal_range[0] > 0 else 0
        else:
            return optimal_range[1] / complexity if complexity > 0 else 0
    
    def _calculate_multiscale_entropy(self, data: np.ndarray) -> List[float]:
        """Calculate Multiscale Entropy (simplified version)"""
        
        scales = [1, 2, 4, 8]  # Different time scales
        multiscale_values = []
        
        for scale in scales:
            if len(data) // scale < 100:  # Need minimum length
                break
                
            # Coarse-grain the signal
            coarse_grained = []
            for i in range(0, len(data) - scale + 1, scale):
                coarse_grained.append(np.mean(data[i:i + scale]))
            
            # Calculate sample entropy for coarse-grained signal
            try:
                r = 0.2 * np.std(coarse_grained)
                ent = sample_entropy(np.array(coarse_grained), m=2, r=r)
                multiscale_values.append(ent)
            except:
                multiscale_values.append(0.0)
        
        return multiscale_values
    
    def _calculate_permutation_entropy(self, data: np.ndarray, order: int = 3) -> float:
        """Calculate Permutation Entropy"""
        
        if len(data) < order:
            return 0.0
        
        # Create ordinal patterns
        patterns = []
        for i in range(len(data) - order + 1):
            segment = data[i:i + order]
            pattern = tuple(np.argsort(segment))
            patterns.append(pattern)
        
        # Count pattern frequencies
        from collections import Counter
        pattern_counts = Counter(patterns)
        
        # Calculate entropy
        total_patterns = len(patterns)
        entropy = 0.0
        
        for count in pattern_counts.values():
            probability = count / total_patterns
            if probability > 0:
                entropy -= probability * np.log2(probability)
        
        # Normalize by maximum possible entropy
        import math
        max_entropy = np.log2(math.factorial(order))
        normalized_entropy = entropy / max_entropy if max_entropy > 0 else 0
        
        return normalized_entropy
    
    def _calculate_channel_complexity_score(self, channel_result: Dict[str, Any]) -> float:
        """Calculate overall complexity score for a channel"""
        
        # Weight different entropy measures
        weights = {
            'sample_entropy_score': 0.25,
            'approximate_entropy_score': 0.20,
            'lempel_ziv_score': 0.20,
            'spectral_entropy_score': 0.15,
            'hjorth_mobility_score': 0.10,
            'hjorth_complexity_score': 0.10
        }
        
        score = 0.0
        for metric, weight in weights.items():
            score += channel_result[metric] * weight
        
        return max(0.0, min(1.0, score))
    
    def _aggregate_channel_results(self, channel_results: List[Dict[str, Any]]):
        """Aggregate results across all channels"""
        
        if not channel_results:
            return
        
        # Calculate statistics across channels
        metrics = ['sample_entropy', 'approximate_entropy', 'lempel_ziv_complexity',
                  'spectral_entropy', 'hjorth_mobility', 'hjorth_complexity',
                  'overall_complexity_score', 'permutation_entropy']
        
        for metric in metrics:
            values = [ch[metric] for ch in channel_results if not np.isnan(ch[metric])]
            if values:
                self.results[f'{metric}_mean'] = np.mean(values)
                self.results[f'{metric}_std'] = np.std(values)
                self.results[f'{metric}_min'] = np.min(values)
                self.results[f'{metric}_max'] = np.max(values)
        
        # Count channels with low complexity
        self.results['channels_low_complexity'] = sum(
            1 for ch in channel_results if ch['overall_complexity_score'] < 0.3
        )
        
        self.results['channels_high_complexity'] = sum(
            1 for ch in channel_results if ch['overall_complexity_score'] > 0.8
        )
        
        # Multiscale entropy aggregation
        multiscale_entropies = [ch['multiscale_entropy'] for ch in channel_results]
        if multiscale_entropies and multiscale_entropies[0]:
            max_scales = max(len(me) for me in multiscale_entropies)
            for scale_idx in range(max_scales):
                scale_values = [me[scale_idx] for me in multiscale_entropies 
                               if len(me) > scale_idx and not np.isnan(me[scale_idx])]
                if scale_values:
                    self.results[f'multiscale_entropy_scale_{scale_idx+1}_mean'] = np.mean(scale_values)
        
        # Store individual channel results
        self.results['channel_details'] = channel_results
    
    def _generate_channel_flags(self, channel_result: Dict[str, Any], channel_idx: int):
        """Generate quality flags for a single channel"""
        
        ch_name = f"Channel {channel_idx + 1}"
        
        # Low complexity flags
        if channel_result['overall_complexity_score'] < 0.2:
            self._add_flag(
                'VERY_LOW_COMPLEXITY', 'HIGH',
                f"{ch_name}: Very low signal complexity (score: {channel_result['overall_complexity_score']:.2f})"
            )
        elif channel_result['overall_complexity_score'] < 0.4:
            self._add_flag(
                'LOW_COMPLEXITY', 'MEDIUM',
                f"{ch_name}: Low signal complexity (score: {channel_result['overall_complexity_score']:.2f})"
            )
        
        # High complexity flags (might indicate noise)
        if channel_result['overall_complexity_score'] > 0.9:
            self._add_flag(
                'VERY_HIGH_COMPLEXITY', 'MEDIUM',
                f"{ch_name}: Very high complexity - possible noise contamination"
            )
        
        # Specific entropy flags
        if channel_result['sample_entropy_score'] < 0.3:
            self._add_flag(
                'POOR_SAMPLE_ENTROPY', 'LOW',
                f"{ch_name}: Sample entropy outside optimal range ({channel_result['sample_entropy']:.2f})"
            )
        
        if channel_result['lempel_ziv_score'] < 0.3:
            self._add_flag(
                'POOR_LZ_COMPLEXITY', 'LOW',
                f"{ch_name}: LZ complexity outside optimal range ({channel_result['lempel_ziv_complexity']:.2f})"
            )
        
        # Hjorth parameter flags
        if channel_result['hjorth_mobility_score'] < 0.3:
            self._add_flag(
                'ABNORMAL_HJORTH_MOBILITY', 'LOW',
                f"{ch_name}: Hjorth mobility outside normal range ({channel_result['hjorth_mobility']:.2f})"
            )
        
        if channel_result['hjorth_complexity_score'] < 0.3:
            self._add_flag(
                'ABNORMAL_HJORTH_COMPLEXITY', 'LOW',
                f"{ch_name}: Hjorth complexity outside normal range ({channel_result['hjorth_complexity']:.2f})"
            )
    
    def _generate_visualizations(self, signal: np.ndarray, channel_results: List[Dict[str, Any]]):
        """Generate visualization specifications"""
        
        # Complexity metrics comparison
        self._add_visualization(
            'complexity_metrics',
            {
                'channel_results': channel_results,
                'metrics': ['sample_entropy', 'approximate_entropy', 'lempel_ziv_complexity', 'spectral_entropy']
            },
            {'title': 'Complexity and Entropy Metrics', 'plot_type': 'radar'}
        )
        
        # Multiscale entropy plot
        self._add_visualization(
            'multiscale_entropy',
            {
                'channel_results': channel_results,
                'sampling_rate': self.sampling_rate
            },
            {'title': 'Multiscale Entropy Analysis', 'plot_type': 'line'}
        )
    
    def get_score(self) -> float:
        """Calculate overall complexity and entropy score (0-100)"""
        
        if not self.results:
            return 0.0
        
        # Use the mean overall complexity score across channels
        mean_complexity = self.results.get('overall_complexity_score_mean', 0)
        
        # Convert to 0-100 scale
        base_score = mean_complexity * 100
        
        # Apply penalties for extreme values
        penalties = 0
        
        # Penalty for too many low complexity channels
        low_complexity_channels = self.results.get('channels_low_complexity', 0)
        total_channels = len(self.results.get('channel_details', []))
        if total_channels > 0:
            low_complexity_ratio = low_complexity_channels / total_channels
            penalties += low_complexity_ratio * 30  # Up to 30 point penalty
        
        # Penalty for too many high complexity channels (possible noise)
        high_complexity_channels = self.results.get('channels_high_complexity', 0)
        if total_channels > 0:
            high_complexity_ratio = high_complexity_channels / total_channels
            penalties += high_complexity_ratio * 20  # Up to 20 point penalty
        
        final_score = base_score - penalties
        return max(0.0, min(100.0, final_score))
    
    def get_recommendations(self) -> List[str]:
        """Generate actionable recommendations"""
        
        recommendations = []
        
        if self.results.get('channels_low_complexity', 0) > 0:
            recommendations.append(
                "Low complexity signals detected - check for flat signals or poor electrode contact"
            )
        
        if self.results.get('channels_high_complexity', 0) > 0:
            recommendations.append(
                "Very high complexity detected - check for noise contamination or artifacts"
            )
        
        if self.results.get('overall_complexity_score_mean', 1) < 0.4:
            recommendations.append(
                "Overall low signal complexity - ensure adequate neural activity and proper recording conditions"
            )
        
        if self.results.get('sample_entropy_mean', 1) < 0.3:
            recommendations.append(
                "Low sample entropy - signal may be too regular or contain artifacts"
            )
        
        # Add general recommendations based on overall score
        score = self.get_score()
        if score < 60:
            recommendations.append(
                "Poor complexity metrics - review signal quality and recording parameters"
            )
        elif score < 80:
            recommendations.append(
                "Moderate complexity - consider preprocessing to optimize signal characteristics"
            )
        
        return recommendations
